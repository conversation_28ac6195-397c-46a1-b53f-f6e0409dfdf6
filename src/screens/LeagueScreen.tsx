import React, { useState } from 'react';
import { ActivityIndicator, FlatList, RefreshControl } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { Text } from '../components/Text';
import { useManager } from '../context/ManagerContext';
import { useCachedLeague } from '../hooks/useCachedData';
import { useTheme } from 'styled-components/native';
import { logger } from '../utils/logger';

interface StyledProps {
  theme: DefaultTheme;
}

interface TableRowProps extends StyledProps {
  isPromotion?: boolean;
  isRelegation?: boolean;
  isMyTeam?: boolean;
}

interface CellProps extends StyledProps {
  flex?: number;
  center?: boolean;
  isMyTeam?: boolean;
}

const Container = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 16px;
`;

const TableContainer = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 8px;
  overflow: hidden;
`;

const TableRow = styled.View<TableRowProps>`
  flex-direction: row;
  padding: 0px 8px;
  background-color: ${({ isPromotion, isRelegation, isMyTeam, theme }: TableRowProps) =>
    isMyTeam
      ? theme.colors.primary
      : isPromotion
        ? '#e6d72c'
        : isRelegation
          ? '#9c2e2e'
          : theme.colors.surface};
`;

const HeaderRow = styled.View`
  flex-direction: row;
  padding: 12px 8px;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.primary};
`;

const Cell = styled(Text)<CellProps>`
  flex: ${({ flex }: CellProps) => flex || 1};
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  text-align: ${({ center }: CellProps) => (center ? 'center' : 'left')};
  font-family: ${({ isMyTeam, theme }: CellProps) =>
    isMyTeam ? theme.typography.bold : theme.typography.regular};
`;

const HeaderCell = styled(Cell)`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const Title = styled(Text)`
  font-size: 24px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  margin-bottom: 8px;
`;

const Tier = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: ${(props: StyledProps) => props.theme.typography.regular};
  margin-bottom: 16px;
`;

const LeagueScreen = () => {
  const { manager, team } = useManager();
  const theme = useTheme();
  const [refreshing, setRefreshing] = useState(false);

  logger.log('LeagueScreen', manager, team);
  const { data: leagueData, isLoading, refetch } = useCachedLeague(manager?.gameworldId, team?.league.id);

  if (isLoading) {
    return (
      <Container>
        <ActivityIndicator size="large" />
      </Container>
    );
  }

  if (!leagueData) {
    return (
      <Container>
        <Title>No data available</Title>
      </Container>
    );
  }

  const sortedTeams = [...leagueData.teams].sort((a, b) => {
    const goalDifferenceA = a.goalsFor - a.goalsAgainst;
    const goalDifferenceB = b.goalsFor - b.goalsAgainst;

    if (a.points !== b.points) {
      return b.points - a.points;
    } else if (goalDifferenceA !== goalDifferenceB) {
      return goalDifferenceB - goalDifferenceA;
    } else if (a.draws !== b.draws) {
      return b.draws - a.draws;
    } else {
      return a.teamName.localeCompare(b.teamName);
    }
  });

  const { promotionSpots, relegationSpots } = leagueData.leagueRules;

  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetch();
    } finally {
      setRefreshing(false);
    }
  };

  const renderTeam = ({ item: t, index }: { item: any; index: number }) => (
    <TableRow
      isPromotion={index < promotionSpots}
      isRelegation={index >= sortedTeams.length - relegationSpots}
      isMyTeam={t.teamId === team?.teamId}
    >
      <Cell isMyTeam={t.teamId === team?.teamId} flex={3}>
        {t.teamName}
      </Cell>
      <Cell isMyTeam={t.teamId === team?.teamId} center>
        {t.played}
      </Cell>
      <Cell isMyTeam={t.teamId === team?.teamId} center>
        {t.wins}
      </Cell>
      <Cell isMyTeam={t.teamId === team?.teamId} center>
        {t.draws}
      </Cell>
      <Cell isMyTeam={t.teamId === team?.teamId} center>
        {t.losses}
      </Cell>
      <Cell isMyTeam={t.teamId === team?.teamId} center>
        {t.goalsFor}
      </Cell>
      <Cell isMyTeam={t.teamId === team?.teamId} center>
        {t.goalsAgainst}
      </Cell>
      <Cell isMyTeam={t.teamId === team?.teamId} center>
        {t.goalsFor - t.goalsAgainst}
      </Cell>
      <Cell isMyTeam={t.teamId === team?.teamId} center>
        {t.points}
      </Cell>
    </TableRow>
  );

  const renderHeader = () => (
    <TableContainer>
      <HeaderRow>
        <HeaderCell flex={3}>Team Name</HeaderCell>
        <HeaderCell center>P</HeaderCell>
        <HeaderCell center>W</HeaderCell>
        <HeaderCell center>D</HeaderCell>
        <HeaderCell center>L</HeaderCell>
        <HeaderCell center>GF</HeaderCell>
        <HeaderCell center>GA</HeaderCell>
        <HeaderCell center>GD</HeaderCell>
        <HeaderCell center>Pts</HeaderCell>
      </HeaderRow>
    </TableContainer>
  );

  return (
    <Container>
      <Title>{leagueData.name}</Title>
      <Tier>Tier {leagueData.tier}</Tier>
      <FlatList
        data={sortedTeams}
        renderItem={renderTeam}
        keyExtractor={(item) => item.teamName}
        ListHeaderComponent={renderHeader}
        contentContainerStyle={{ padding: 16 }}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      />
    </Container>
  );
};

export default LeagueScreen;
