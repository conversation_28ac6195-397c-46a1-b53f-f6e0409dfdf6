import React, { useEffect } from 'react';
import { Modal } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { useDataCache } from '../context/DataCacheContext';
import { useManager } from '../context/ManagerContext';
import { usePlayerUtils } from '../context/PlayerContext';
import { ActiveTransfer } from '../hooks/useMyBidsPlayers';
import { CorePlayer, Player, TransferListPlayer } from '../models/player';
import { Team } from '../models/team';
import { CrossPlatformAlert } from './CrossPlatformAlert';
import { AttributeTable } from './PlayerDetail/AttributeTable';
import { AuctionSection } from './PlayerDetail/AuctionSection';
import { ContractSection } from './PlayerDetail/ContractSection';
import { useMagicSponge } from './PlayerDetail/hooks/useMagicSponge';
import { usePlayerStatus } from './PlayerDetail/hooks/usePlayerStatus';
import { useRedCardAppeal } from './PlayerDetail/hooks/useRedCardAppeal';
import { MagicSpongeModal } from './PlayerDetail/MagicSpongeModal';
import { PlayerHeader } from './PlayerDetail/PlayerHeader';
import { PlayerInfo } from './PlayerDetail/PlayerInfo';
import { PlayerStatus } from './PlayerDetail/PlayerStatus';
import { RedCardAppealModal } from './PlayerDetail/RedCardAppealModal';
import { ReleasePlayerModal } from './PlayerDetail/ReleasePlayerModal';
import { TransferModal } from './PlayerDetail/TransferModal';
import { TransferSection } from './PlayerDetail/TransferSection';

interface StyledProps {
  theme: DefaultTheme;
}

interface PlayerDetailViewProps {
  player: CorePlayer;
  team?: Team;
  onClose: () => void;
  onNewTransfer: (transfer: ActiveTransfer) => void;
  onNegotiate: (transfer: ActiveTransfer) => void;
}

const DetailView = styled.View`
  flex: 1;
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 16px;
  width: 100%;
  height: 100%;
`;

const ScrollContainer = styled.ScrollView`
  flex: 1;
`;

const PlayerDetailView: React.FC<PlayerDetailViewProps> = ({
  player,
  team,
  onClose,
  onNewTransfer,
  onNegotiate,
}) => {
  const { manager, team: userTeam } = useManager();
  const { findPlayer } = useDataCache();
  const [magicSpongeState, magicSpongeActions] = useMagicSponge();
  const [redCardAppealState, redCardAppealActions] = useRedCardAppeal();

  // Get the current player data from cache to ensure we show updated information
  const currentPlayer = findPlayer(player.playerId) as CorePlayer | null;
  const playerToRender = currentPlayer || player; // Fallback to original player if not found in cache

  // Custom hooks
  const playerStatus = usePlayerStatus(playerToRender, userTeam || undefined, team);
  const { playerActionsState, playerActions } = usePlayerUtils();

  const [isModalVisible, setModalVisible] = React.useState(false);

  // Group attributes by category
  const goalkeepingAttributes = [
    {
      name: 'Reflexes',
      value: playerToRender.attributes.reflexes,
      potential: playerToRender.potentialAttributes?.reflexes,
    },
    {
      name: 'Positioning',
      value: playerToRender.attributes.positioning,
      potential: playerToRender.potentialAttributes?.positioning,
    },
    {
      name: 'Shot Stopping',
      value: playerToRender.attributes.shotStopping,
      potential: playerToRender.potentialAttributes?.shotStopping,
    },
  ];

  const defendingAttributes = [
    {
      name: 'Tackling',
      value: playerToRender.attributes.tackling,
      potential: playerToRender.potentialAttributes?.tackling,
    },
    {
      name: 'Marking',
      value: playerToRender.attributes.marking,
      potential: playerToRender.potentialAttributes?.marking,
    },
    {
      name: 'Heading',
      value: playerToRender.attributes.heading,
      potential: playerToRender.potentialAttributes?.heading,
    },
  ];

  const midfieldingAttributes = [
    {
      name: 'Passing',
      value: playerToRender.attributes.passing,
      potential: playerToRender.potentialAttributes?.passing,
    },
    {
      name: 'Vision',
      value: playerToRender.attributes.vision,
      potential: playerToRender.potentialAttributes?.vision,
    },
    {
      name: 'Ball Control',
      value: playerToRender.attributes.ballControl,
      potential: playerToRender.potentialAttributes?.ballControl,
    },
  ];

  const attackingAttributes = [
    {
      name: 'Finishing',
      value: playerToRender.attributes.finishing,
      potential: playerToRender.potentialAttributes?.finishing,
    },
    {
      name: 'Pace',
      value: playerToRender.attributes.pace,
      potential: playerToRender.potentialAttributes?.pace,
    },
    {
      name: 'Crossing',
      value: playerToRender.attributes.crossing,
      potential: playerToRender.potentialAttributes?.crossing,
    },
  ];

  const adminAttributes = [{ name: 'Stamina', value: playerToRender.attributes.stamina }];

  useEffect(() => {
    if (playerToRender) {
      setModalVisible(true);
    }
  }, [playerToRender]);

  return (
    <>
      <Modal
        visible={isModalVisible}
        transparent={false}
        animationType="slide"
        onRequestClose={onClose}
      >
        <DetailView>
          <PlayerHeader player={playerToRender} onClose={onClose} />

          <MagicSpongeModal
            player={playerToRender as Player}
            isInjured={playerStatus.isInjured}
            spongesAvailable={manager?.magicSponges ?? 0}
            magicSpongeState={magicSpongeState}
            magicSpongeActions={magicSpongeActions}
          />

          <RedCardAppealModal
            player={playerToRender as Player}
            isSuspended={playerStatus.isSuspended}
            appealsAvailable={manager?.cardAppeals ?? 0}
            redCardAppealState={redCardAppealState}
            redCardAppealActions={redCardAppealActions}
          />

          <ScrollContainer showsVerticalScrollIndicator={false}>
            <PlayerInfo
              player={playerToRender}
              team={team}
              playerStatus={playerStatus}
              magicSpongeActions={magicSpongeActions}
              redCardAppealActions={redCardAppealActions}
            />

            {!playerStatus.isPlayerInUserTeam && playerStatus.isAuctionPlayer && (
              <AuctionSection
                player={playerToRender as unknown as TransferListPlayer}
                playerStatus={playerStatus}
                isPlayerInUserTeam={playerStatus.isPlayerInUserTeam}
                maxBid={playerActionsState.maxBid}
                isHighestBidder={playerActionsState.isHighestBidder}
              />
            )}

            {!playerStatus.isPlayerInUserTeam && !playerStatus.isAuctionPlayer && (
              <TransferSection
                player={playerToRender as unknown as TransferListPlayer}
                playerStatus={playerStatus}
                onNegotiate={onNegotiate}
              />
            )}

            {playerStatus.isPlayerInUserTeam && (
              <>
                <ContractSection player={playerToRender} />
                <PlayerStatus
                  player={playerToRender as Player}
                  isInjured={playerStatus.isInjured}
                  isSuspended={playerStatus.isSuspended}
                />
              </>
            )}

            <AttributeTable
              title="Goalkeeping"
              attributes={goalkeepingAttributes}
              floorAttributes={manager?.role !== 'admin'}
            />
            <AttributeTable
              title="Defending"
              attributes={defendingAttributes}
              floorAttributes={manager?.role !== 'admin'}
            />
            <AttributeTable
              title="Midfielding"
              attributes={midfieldingAttributes}
              floorAttributes={manager?.role !== 'admin'}
            />
            <AttributeTable
              title="Attacking"
              attributes={attackingAttributes}
              floorAttributes={manager?.role !== 'admin'}
            />
            {manager?.role === 'admin' && (
              <AttributeTable
                title="Admin"
                attributes={adminAttributes}
                floorAttributes={manager?.role !== 'admin'}
              />
            )}
          </ScrollContainer>

          <TransferModal
            player={playerToRender as unknown as TransferListPlayer}
            playerStatus={playerStatus}
            isAuctionPlayer={playerStatus.isAuctionPlayer}
            formattedValue={playerStatus.formattedValue}
            onClose={() => playerActions.setIsOfferModalVisible(false)}
            onNewTransfer={onNewTransfer}
          />

          {playerStatus.isPlayerInUserTeam && (
            <ReleasePlayerModal player={playerToRender as Player} />
          )}

          <CrossPlatformAlert
            visible={playerActionsState.showAlert}
            title={playerActionsState.alertMessage.title}
            message={playerActionsState.alertMessage.message}
            buttons={[
              {
                text: 'OK',
                onPress: () => {
                  playerActions.setShowAlert(false);
                  onClose();
                },
              },
            ]}
            onDismiss={() => playerActions.setShowAlert(false)}
          />
        </DetailView>
      </Modal>
    </>
  );
};

export default PlayerDetailView;
