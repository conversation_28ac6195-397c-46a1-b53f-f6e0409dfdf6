import React, { useState } from 'react';
import { TouchableOpacity } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { DetailedPlayerData } from '../../models/player';
import { Text } from '../Text';
import { PlayerOverallStats } from './PlayerOverallStats';
import { PlayerMatchHistory } from './PlayerMatchHistory';

interface StyledProps {
  theme: DefaultTheme;
}

interface TabProps extends StyledProps {
  isActive: boolean;
}

interface PlayerDetailTabsProps {
  playerDetails: DetailedPlayerData | null;
  isLoading: boolean;
}

const TabContainer = styled.View`
  flex-direction: row;
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.primary};
  margin-bottom: 16px;
`;

const Tab = styled(TouchableOpacity)<TabProps>`
  flex: 1;
  padding: 12px;
  align-items: center;
  border-bottom-width: 2px;
  border-bottom-color: ${(props) => (props.isActive ? props.theme.colors.primary : 'transparent')};
`;

const TabText = styled(Text)<TabProps>`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 14px;
  color: ${(props) =>
    props.isActive ? props.theme.colors.text.primary : props.theme.colors.text.secondary};
`;

const ContentContainer = styled.View`
  flex: 1;
`;

export const PlayerDetailTabs: React.FC<PlayerDetailTabsProps> = ({
  playerDetails,
  isLoading,
}) => {
  const [activeTab, setActiveTab] = useState<'stats' | 'history'>('stats');

  const renderContent = () => {
    if (isLoading) {
      return null; // Loading will be handled by parent
    }

    if (!playerDetails) {
      return null;
    }

    switch (activeTab) {
      case 'stats':
        return <PlayerOverallStats overallStats={playerDetails.overallStats} />;
      case 'history':
        return <PlayerMatchHistory matchHistory={playerDetails.matchHistory} />;
      default:
        return null;
    }
  };

  return (
    <>
      <TabContainer>
        <Tab isActive={activeTab === 'stats'} onPress={() => setActiveTab('stats')}>
          <TabText isActive={activeTab === 'stats'}>OVERALL STATS</TabText>
        </Tab>
        <Tab isActive={activeTab === 'history'} onPress={() => setActiveTab('history')}>
          <TabText isActive={activeTab === 'history'}>MATCH HISTORY</TabText>
        </Tab>
      </TabContainer>
      <ContentContainer>{renderContent()}</ContentContainer>
    </>
  );
};
