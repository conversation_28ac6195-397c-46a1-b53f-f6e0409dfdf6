import React, { useState } from 'react';
import { ScrollView, TouchableOpacity } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { CorePlayer, DetailedPlayerData } from '../../models/player';
import { Text } from '../Text';
import { AttributeTable } from './AttributeTable';
import { PlayerOverallStats } from './PlayerOverallStats';
import { PlayerMatchHistory } from './PlayerMatchHistory';

interface StyledProps {
  theme: DefaultTheme;
}

interface TabProps extends StyledProps {
  isActive: boolean;
}

interface PlayerDetailTabsProps {
  player: CorePlayer;
  playerDetails: DetailedPlayerData | null;
  isLoading: boolean;
  isAdmin?: boolean;
}

const TabScrollContainer = styled.View`
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.primary};
  margin-bottom: 16px;
`;

const TabContainer = styled(ScrollView)`
  flex-direction: row;
`;

const Tab = styled(TouchableOpacity)<TabProps>`
  padding: 12px 16px;
  align-items: center;
  border-bottom-width: 2px;
  border-bottom-color: ${(props) => (props.isActive ? props.theme.colors.primary : 'transparent')};
  min-width: 120px;
`;

const TabText = styled(Text)<TabProps>`
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  font-size: 12px;
  color: ${(props) =>
    props.isActive ? props.theme.colors.text.primary : props.theme.colors.text.secondary};
  text-align: center;
`;

const ContentContainer = styled.View`
  flex: 1;
`;

export const PlayerDetailTabs: React.FC<PlayerDetailTabsProps> = ({
  player,
  playerDetails,
  isLoading,
  isAdmin = false,
}) => {
  const [activeTab, setActiveTab] = useState<'attributes' | 'stats' | 'history'>('attributes');

  // Debug logging
  console.log('PlayerDetailTabs - playerDetails:', playerDetails);
  console.log('PlayerDetailTabs - matchHistory length:', playerDetails?.matchHistory?.length || 0);

  // Group attributes by category
  const goalkeepingAttributes = [
    {
      name: 'Reflexes',
      value: player.attributes.reflexes,
      potential: player.potentialAttributes?.reflexes,
    },
    {
      name: 'Positioning',
      value: player.attributes.positioning,
      potential: player.potentialAttributes?.positioning,
    },
    {
      name: 'Shot Stopping',
      value: player.attributes.shotStopping,
      potential: player.potentialAttributes?.shotStopping,
    },
  ];

  const defendingAttributes = [
    {
      name: 'Tackling',
      value: player.attributes.tackling,
      potential: player.potentialAttributes?.tackling,
    },
    {
      name: 'Marking',
      value: player.attributes.marking,
      potential: player.potentialAttributes?.marking,
    },
    {
      name: 'Heading',
      value: player.attributes.heading,
      potential: player.potentialAttributes?.heading,
    },
  ];

  const midfieldingAttributes = [
    {
      name: 'Passing',
      value: player.attributes.passing,
      potential: player.potentialAttributes?.passing,
    },
    {
      name: 'Vision',
      value: player.attributes.vision,
      potential: player.potentialAttributes?.vision,
    },
    {
      name: 'Ball Control',
      value: player.attributes.ballControl,
      potential: player.potentialAttributes?.ballControl,
    },
  ];

  const attackingAttributes = [
    {
      name: 'Finishing',
      value: player.attributes.finishing,
      potential: player.potentialAttributes?.finishing,
    },
    {
      name: 'Pace',
      value: player.attributes.pace,
      potential: player.potentialAttributes?.pace,
    },
    {
      name: 'Crossing',
      value: player.attributes.crossing,
      potential: player.potentialAttributes?.crossing,
    },
  ];

  const adminAttributes = [{ name: 'Stamina', value: player.attributes.stamina }];

  const renderContent = () => {
    if (isLoading) {
      return null; // Loading will be handled by parent
    }

    switch (activeTab) {
      case 'attributes':
        return (
          <>
            <AttributeTable
              title="Goalkeeping"
              attributes={goalkeepingAttributes}
              floorAttributes={!isAdmin}
            />
            <AttributeTable
              title="Defending"
              attributes={defendingAttributes}
              floorAttributes={!isAdmin}
            />
            <AttributeTable
              title="Midfielding"
              attributes={midfieldingAttributes}
              floorAttributes={!isAdmin}
            />
            <AttributeTable
              title="Attacking"
              attributes={attackingAttributes}
              floorAttributes={!isAdmin}
            />
            {isAdmin && (
              <AttributeTable
                title="Admin"
                attributes={adminAttributes}
                floorAttributes={!isAdmin}
              />
            )}
          </>
        );
      case 'stats':
        if (!playerDetails) return null;
        return <PlayerOverallStats overallStats={playerDetails.overallStats} />;
      case 'history':
        if (!playerDetails) return null;
        return <PlayerMatchHistory matchHistory={playerDetails.matchHistory} />;
      default:
        return null;
    }
  };

  return (
    <>
      <TabScrollContainer>
        <TabContainer
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ paddingHorizontal: 0 }}
        >
          <Tab isActive={activeTab === 'attributes'} onPress={() => setActiveTab('attributes')}>
            <TabText isActive={activeTab === 'attributes'}>ATTRIBUTES</TabText>
          </Tab>
          <Tab isActive={activeTab === 'stats'} onPress={() => setActiveTab('stats')}>
            <TabText isActive={activeTab === 'stats'}>OVERALL STATS</TabText>
          </Tab>
          <Tab isActive={activeTab === 'history'} onPress={() => setActiveTab('history')}>
            <TabText isActive={activeTab === 'history'}>MATCH HISTORY</TabText>
          </Tab>
        </TabContainer>
      </TabScrollContainer>
      <ContentContainer>{renderContent()}</ContentContainer>
    </>
  );
};
