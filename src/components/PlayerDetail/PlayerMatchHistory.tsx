import { MaterialIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import React from 'react';
import { FlatList, TouchableOpacity } from 'react-native';
import styled, { DefaultTheme } from 'styled-components/native';
import { PlayerMatchHistory as PlayerMatchHistoryType } from '../../models/player';
import { Text } from '../Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface PlayerMatchHistoryProps {
  matchHistory: PlayerMatchHistoryType[];
}

const Container = styled.View`
  flex: 1;
`;

const MatchRow = styled(TouchableOpacity)`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  padding: 16px;
  margin-bottom: 8px;
  border-radius: 8px;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
`;

const MatchInfo = styled.View`
  flex: 1;
`;

const MatchId = styled(Text)`
  font-size: 12px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  margin-bottom: 4px;
`;

const StatsContainer = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 8px;
`;

const StatChip = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.background};
  padding: 4px 8px;
  border-radius: 12px;
  flex-direction: row;
  align-items: center;
`;

const StatIcon = styled.View`
  margin-right: 4px;
`;

const StatText = styled(Text)`
  font-size: 12px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const ChevronIcon = styled.View`
  margin-left: 8px;
`;

const NoDataText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  text-align: center;
  padding: 32px;
`;

const StatChipContainer = styled.View`
  flex-direction: row;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
`;

export const PlayerMatchHistory: React.FC<PlayerMatchHistoryProps> = ({ matchHistory }) => {
  const router = useRouter();

  const handleMatchPress = (fixtureId: string) => {
    router.push(`/fixture-detail/${fixtureId}`);
  };

  const renderStatChip = (icon: string, value: number, color: string = '#666') => {
    if (value === 0) return null;
    
    return (
      <StatChip key={icon}>
        <StatIcon>
          <MaterialIcons name={icon as any} size={14} color={color} />
        </StatIcon>
        <StatText>{value > 1 ? value : ''}</StatText>
      </StatChip>
    );
  };

  const renderMatchItem = ({ item }: { item: PlayerMatchHistoryType }) => {
    const hasStats = item.goals > 0 || item.yellowCards > 0 || item.redCards > 0 || 
                    item.tackles > 0 || item.saves > 0 || item.shots > 0;

    return (
      <MatchRow onPress={() => handleMatchPress(item.fixtureId)}>
        <MatchInfo>
          <MatchId>Match ID: {item.fixtureId}</MatchId>
          
          {hasStats && (
            <StatChipContainer>
              {/* Goals */}
              {renderStatChip('sports-soccer', item.goals, '#4CAF50')}
              
              {/* Shots */}
              {renderStatChip('gps-fixed', item.shots, '#2196F3')}
              
              {/* Shots on Target */}
              {renderStatChip('my-location', item.shotsOnTarget, '#FF9800')}
              
              {/* Tackles */}
              {renderStatChip('security', item.tackles, '#9C27B0')}
              
              {/* Saves */}
              {renderStatChip('pan-tool', item.saves, '#00BCD4')}
              
              {/* Yellow Cards */}
              {renderStatChip('warning', item.yellowCards, '#FFC107')}
              
              {/* Red Cards */}
              {renderStatChip('block', item.redCards, '#F44336')}
            </StatChipContainer>
          )}
          
          {!hasStats && (
            <Text style={{ fontSize: 12, color: '#999', fontStyle: 'italic' }}>
              No notable stats this match
            </Text>
          )}
        </MatchInfo>
        
        <ChevronIcon>
          <MaterialIcons name="chevron-right" size={24} color="#666" />
        </ChevronIcon>
      </MatchRow>
    );
  };

  if (!matchHistory || matchHistory.length === 0) {
    return (
      <Container>
        <NoDataText>No match history available for this player this season.</NoDataText>
      </Container>
    );
  }

  return (
    <Container>
      <FlatList
        data={matchHistory}
        renderItem={renderMatchItem}
        keyExtractor={(item) => item.fixtureId}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: 16 }}
      />
    </Container>
  );
};
