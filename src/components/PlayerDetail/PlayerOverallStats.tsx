import React from 'react';
import styled, { DefaultTheme } from 'styled-components/native';
import { PlayerStats } from '../../models/player';
import { Text } from '../Text';

interface StyledProps {
  theme: DefaultTheme;
}

interface PlayerOverallStatsProps {
  overallStats: PlayerStats | null;
}

const Container = styled.View`
  flex: 1;
`;

const StatsSection = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.surface};
  border-radius: 8px;
  margin-bottom: 16px;
`;

const SectionHeader = styled.View`
  background-color: ${(props: StyledProps) => props.theme.colors.primary};
  padding: 12px;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
`;

const SectionTitle = styled(Text)`
  font-size: 16px;
  color: white;
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
`;

const StatRow = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom-width: 1px;
  border-bottom-color: ${(props: StyledProps) => props.theme.colors.border};
`;

const StatLabel = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: 'Nunito';
  flex: 1;
`;

const StatValue = styled(Text)`
  font-size: 14px;
  color: ${(props: StyledProps) => props.theme.colors.text.primary};
  font-family: ${(props: StyledProps) => props.theme.typography.bold};
  min-width: 40px;
  text-align: right;
`;

const NoDataText = styled(Text)`
  font-size: 16px;
  color: ${(props: StyledProps) => props.theme.colors.text.secondary};
  font-family: 'Nunito';
  text-align: center;
  padding: 32px;
`;

export const PlayerOverallStats: React.FC<PlayerOverallStatsProps> = ({ overallStats }) => {
  if (!overallStats) {
    return (
      <Container>
        <NoDataText>No overall statistics available for this player.</NoDataText>
      </Container>
    );
  }

  const calculatePassAccuracy = () => {
    if (overallStats.passesAttempted === 0) return 0;
    return Math.round((overallStats.passesCompleted / overallStats.passesAttempted) * 100);
  };

  const calculateCarrySuccess = () => {
    if (overallStats.ballCarriesAttempted === 0) return 0;
    return Math.round((overallStats.successfulBallCarries / overallStats.ballCarriesAttempted) * 100);
  };

  const calculateShotAccuracy = () => {
    if (overallStats.shots === 0) return 0;
    return Math.round((overallStats.shotsOnTarget / overallStats.shots) * 100);
  };

  return (
    <Container>
      {/* Attacking Stats */}
      <StatsSection>
        <SectionHeader>
          <SectionTitle>ATTACKING</SectionTitle>
        </SectionHeader>
        <StatRow>
          <StatLabel>Goals</StatLabel>
          <StatValue>{overallStats.goals}</StatValue>
        </StatRow>
        <StatRow>
          <StatLabel>Shots</StatLabel>
          <StatValue>{overallStats.shots}</StatValue>
        </StatRow>
        <StatRow>
          <StatLabel>Shots on Target</StatLabel>
          <StatValue>{overallStats.shotsOnTarget}</StatValue>
        </StatRow>
        <StatRow>
          <StatLabel>Shot Accuracy</StatLabel>
          <StatValue>{calculateShotAccuracy()}%</StatValue>
        </StatRow>
      </StatsSection>

      {/* Passing Stats */}
      <StatsSection>
        <SectionHeader>
          <SectionTitle>PASSING</SectionTitle>
        </SectionHeader>
        <StatRow>
          <StatLabel>Passes Completed</StatLabel>
          <StatValue>{overallStats.passesCompleted}</StatValue>
        </StatRow>
        <StatRow>
          <StatLabel>Passes Attempted</StatLabel>
          <StatValue>{overallStats.passesAttempted}</StatValue>
        </StatRow>
        <StatRow>
          <StatLabel>Pass Accuracy</StatLabel>
          <StatValue>{calculatePassAccuracy()}%</StatValue>
        </StatRow>
      </StatsSection>

      {/* Ball Control Stats */}
      <StatsSection>
        <SectionHeader>
          <SectionTitle>BALL CONTROL</SectionTitle>
        </SectionHeader>
        <StatRow>
          <StatLabel>Successful Ball Carries</StatLabel>
          <StatValue>{overallStats.successfulBallCarries}</StatValue>
        </StatRow>
        <StatRow>
          <StatLabel>Ball Carries Attempted</StatLabel>
          <StatValue>{overallStats.ballCarriesAttempted}</StatValue>
        </StatRow>
        <StatRow>
          <StatLabel>Carry Success Rate</StatLabel>
          <StatValue>{calculateCarrySuccess()}%</StatValue>
        </StatRow>
      </StatsSection>

      {/* Defensive Stats */}
      <StatsSection>
        <SectionHeader>
          <SectionTitle>DEFENSIVE</SectionTitle>
        </SectionHeader>
        <StatRow>
          <StatLabel>Tackles</StatLabel>
          <StatValue>{overallStats.tackles}</StatValue>
        </StatRow>
        <StatRow>
          <StatLabel>Saves</StatLabel>
          <StatValue>{overallStats.saves}</StatValue>
        </StatRow>
      </StatsSection>

      {/* Disciplinary Stats */}
      <StatsSection>
        <SectionHeader>
          <SectionTitle>DISCIPLINARY</SectionTitle>
        </SectionHeader>
        <StatRow>
          <StatLabel>Fouls</StatLabel>
          <StatValue>{overallStats.fouls}</StatValue>
        </StatRow>
        <StatRow>
          <StatLabel>Yellow Cards</StatLabel>
          <StatValue>{overallStats.yellowCards}</StatValue>
        </StatRow>
        <StatRow>
          <StatLabel>Red Cards</StatLabel>
          <StatValue>{overallStats.redCards}</StatValue>
        </StatRow>
      </StatsSection>
    </Container>
  );
};
