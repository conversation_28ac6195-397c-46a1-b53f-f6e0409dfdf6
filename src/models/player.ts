import { PartialSome } from '../utils/typeUtils';

export interface Attributes {
  isGoalkeeper: boolean;

  // goalkeeper
  reflexes: number;
  positioning: number;
  shotStopping: number;
  // defender
  tackling: number;
  marking: number;
  heading: number;
  // attacker
  finishing: number;
  pace: number;
  crossing: number;
  // midfielder
  passing: number;
  vision: number;
  ballControl: number;

  stamina: number;
}

export interface Player {
  gameworldId: string;
  firstName: string;
  surname: string;
  teamId: string;
  attributes: Attributes;
  potentialAttributes: Omit<Attributes, 'isGoalkeeper' | 'stamina'> | undefined;
  value: number;
  age: number;
  playerId: string;
  energy: number; // 0-100
  lastMatchPlayed: number;
  injuredUntil?: number;
  suspendedForGames: number;
  retiringAtEndOfSeason: boolean;
  trainingPotential: number; // 0-1 how much potential the player has left to train
  topTrainingAreas: string[]; // the top 3 training areas for the player
  isTransferListed: boolean;
}

export function calculateBestPosition(player: CorePlayer): string[] {
  const attributes = player.attributes;
  if (attributes.isGoalkeeper) {
    return ['GK'];
  }
  const attributeValues = Object.values(attributes);
  const average = attributeValues.reduce((sum, value) => sum + value, 0) / attributeValues.length;

  const positions: string[] = [];

  const dfAttributes = (attributes.tackling + attributes.marking + attributes.heading) / 3;
  const cmAttributes = (attributes.passing + attributes.vision + attributes.ballControl) / 3;
  const fwdAttributes = (attributes.finishing + attributes.pace + attributes.crossing) / 3;

  const attributeScores = [
    { position: 'DF', score: dfAttributes },
    { position: 'FWD', score: fwdAttributes },
    { position: 'CM', score: cmAttributes },
  ];

  const bestScore = Math.max(...attributeScores.map((attr) => attr.score));

  attributeScores.forEach((attr) => {
    if (attr.score >= average + 5 && attr.score === bestScore) {
      positions.push(attr.position);
    }
  });

  if (positions.length === 0) {
    const bestPosition = attributeScores.find((attr) => attr.score === bestScore);
    if (bestPosition) {
      positions.push(bestPosition.position);
    }
  }

  return positions;
}

export interface TransferListPlayer {
  auctionStartPrice: number;
  auctionCurrentPrice: number;
  auctionEndTime: number;
  bidHistory: { teamId: string; maximumBid: number; bidTime: number; teamName: string }[];
  gameworldId: string;
  teamId?: string;
  leagueId?: string;
  playerId: string;
  firstName: string;
  surname: string;
  attributes: Attributes;
  age: number;
  value: number;
}

export interface PlayerStats {
  yellowCards: number;
  redCards: number;
  passesCompleted: number;
  passesAttempted: number;
  successfulBallCarries: number;
  ballCarriesAttempted: number;
  shots: number;
  shotsOnTarget: number;
  goals: number;
  saves: number;
  tackles: number;
  fouls: number;
}

export interface PlayerMatchHistory extends PlayerStats {
  fixtureId: string;
}

export interface DetailedPlayerData {
  playerId: string;
  gameworldId: string;
  firstName: string;
  surname: string;
  age: number;
  value: number;
  energy: number;
  overallStats: PlayerStats | null;
  matchHistory: PlayerMatchHistory[];
}

export type CorePlayer = PartialSome<
  Player,
  | 'teamId'
  | 'energy'
  | 'lastMatchPlayed'
  | 'suspendedForGames'
  | 'retiringAtEndOfSeason'
  | 'trainingPotential'
  | 'topTrainingAreas'
>;
