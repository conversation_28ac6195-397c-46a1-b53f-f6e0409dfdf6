import { useEffect, useMemo } from 'react';
import { useDataCache } from '../context/DataCacheContext';
import { isCacheStale } from '../utils/cacheUtils';
import { logger } from '../utils/logger';
import { useMyActiveTransfers, useMyBidsPlayers } from './useMyBidsPlayers';
import { useFixtures, useLeague, useManagerQuery, usePlayerDetails, useScoutedPlayers, useTeam } from './useQueries';
import { useTransferListPlayers, TransferListFilters } from './useTransferListPlayers';

/**
 * Hook that provides cached manager data and automatically syncs with API
 */
export const useCachedManager = () => {
  const { cache, setManager, updateManager } = useDataCache();
  const { data: apiManager, isLoading, error, refetch } = useManagerQuery();

  // Sync API data to cache when it changes
  useEffect(() => {
    if (apiManager) {
      setManager(apiManager);
    }
  }, [apiManager, setManager]);

  // Check if we should refetch based on cache staleness
  const shouldRefetch = isCacheStale(cache.lastUpdated.manager, 5); // 5 minutes

  return {
    manager: cache.manager || apiManager,
    isLoading,
    error,
    refetch,
    updateManager,
    shouldRefetch,
  };
};

/**
 * Hook that provides cached team data and automatically syncs with API
 */
export const useCachedTeam = (gameworldId?: string, teamId?: string) => {
  const { cache, setTeam, updateTeam } = useDataCache();
  const { data: apiTeam, isLoading, error, refetch } = useTeam(gameworldId, teamId);

  // Sync API data to cache when it changes
  useEffect(() => {
    if (apiTeam) {
      setTeam(apiTeam);
    }
  }, [apiTeam, setTeam]);

  // Check if we should refetch based on cache staleness
  const shouldRefetch = isCacheStale(cache.lastUpdated.team, 5); // 5 minutes

  return {
    team: cache.team || apiTeam,
    isLoading,
    error,
    refetch,
    updateTeam,
    shouldRefetch,
  };
};

/**
 * Hook that provides cached transfer list players and automatically syncs with API
 */
export const useCachedTransferListPlayers = (gameworldId?: string, filters?: TransferListFilters) => {
  const { cache, setTransferListPlayers, addTransferListPlayers } = useDataCache();
  const {
    data: apiData,
    isLoading,
    error,
    refetch,
  } = useTransferListPlayers(gameworldId, filters);

  // Sync API data to cache when it changes
  useEffect(() => {
    if (apiData?.players) {
      if (!filters?.lastEvaluatedKey) {
        // Initial load - replace all data
        setTransferListPlayers(apiData.players);
      } else {
        // Pagination load - add to existing data
        addTransferListPlayers(apiData.players);
      }
    }
  }, [apiData, filters?.lastEvaluatedKey, setTransferListPlayers, addTransferListPlayers]);

  // Check if we should refetch based on cache staleness
  const shouldRefetch = isCacheStale(cache.lastUpdated.players, 3); // 3 minutes for transfer list

  return {
    data: {
      players: cache.players.transferListPlayers,
      ...apiData,
    },
    isLoading,
    error,
    refetch,
    shouldRefetch,
  };
};

/**
 * Hook that provides cached scouted players and automatically syncs with API
 */
export const useCachedScoutedPlayers = (
  gameworldId?: string,
  teamId?: string,
  nextToken?: string
) => {
  const { cache, setScoutedPlayers, addScoutedPlayers } = useDataCache();
  const {
    data: apiData,
    isLoading,
    error,
    refetch,
  } = useScoutedPlayers(gameworldId, teamId, nextToken);

  // Sync API data to cache when it changes
  useEffect(() => {
    if (apiData?.scoutedPlayers) {
      if (!nextToken) {
        // Initial load - replace all data
        setScoutedPlayers(apiData.scoutedPlayers);
      } else {
        // Pagination load - add to existing data
        addScoutedPlayers(apiData.scoutedPlayers);
      }
    }
  }, [apiData, nextToken, setScoutedPlayers, addScoutedPlayers]);

  // Check if we should refetch based on cache staleness
  const shouldRefetch = isCacheStale(cache.lastUpdated.players, 10); // 10 minutes for scouted players

  return {
    data: apiData
      ? {
          ...apiData,
          scoutedPlayers: cache.players.scoutedPlayers,
        }
      : undefined,
    isLoading,
    error,
    refetch,
    shouldRefetch,
  };
};

/**
 * Hook that provides cached my bids players and automatically syncs with API
 */
export const useCachedMyBidsPlayers = (gameworldId?: string) => {
  const { cache, setMyBidsPlayers } = useDataCache();
  const { data: apiData, isLoading, error, refetch } = useMyBidsPlayers(gameworldId);

  // Sync API data to cache when it changes
  useEffect(() => {
    if (apiData?.players) {
      setMyBidsPlayers(apiData.players);
    }
  }, [apiData, setMyBidsPlayers]);

  // Check if we should refetch based on cache staleness
  const shouldRefetch = isCacheStale(cache.lastUpdated.players, 2); // 2 minutes for my bids

  return {
    data: {
      players: cache.players.myBidsPlayers,
      ...apiData,
    },
    isLoading,
    error,
    refetch,
    shouldRefetch,
  };
};

/**
 * Hook that provides cached my active transfers and automatically syncs with API
 */
export const useCachedMyActiveTransfers = (gameworldId?: string) => {
  const { cache, setMyActiveTransfers } = useDataCache();
  const { data: apiData, isLoading, error, refetch } = useMyActiveTransfers(gameworldId);

  // Sync API data to cache when it changes
  useEffect(() => {
    if (apiData?.transfers) {
      setMyActiveTransfers(apiData.transfers);
    }
  }, [apiData, setMyActiveTransfers]);

  // Check if we should refetch based on cache staleness
  const shouldRefetch = isCacheStale(cache.lastUpdated.players, 2); // 2 minutes for active transfers

  return {
    data: {
      transfers: cache.players.myActiveTransfers,
      ...apiData,
    },
    isLoading,
    error,
    refetch,
    shouldRefetch,
  };
};

/**
 * Hook that provides cached fixtures and automatically syncs with API
 */
export const useCachedFixtures = (gameworldId?: string, leagueId?: string, teamId?: string) => {
  const { cache, setFixtures } = useDataCache();
  const { data: apiData, isLoading, error, refetch } = useFixtures(gameworldId, leagueId, teamId);

  // Sync API data to cache when it changes
  useEffect(() => {
    if (apiData) {
      setFixtures(apiData);
    }
  }, [apiData, setFixtures]);

  // Check if we should refetch based on cache staleness
  const shouldRefetch = isCacheStale(cache.lastUpdated.fixtures, 360); // 6 hours for fixtures (updates twice daily)

  // Returns the next fixture (future and not yet played), memoized with useMemo
  const fixturesList = cache.fixtures.length > 0 ? cache.fixtures : apiData;
  const getNextFixture = useMemo(() => {
    if (!fixturesList || fixturesList.length === 0) return undefined;
    const now = Date.now();
    return fixturesList.filter((f) => !f.played && f.date > now).sort((a, b) => a.date - b.date)[0];
  }, [fixturesList]);

  return {
    data: fixturesList,
    isLoading,
    error,
    refetch,
    shouldRefetch,
    getNextFixture,
  };
};

/**
 * Hook that provides cached league data and automatically syncs with API
 */
export const useCachedLeague = (gameworldId?: string, leagueId?: string) => {
  const { cache, setLeague } = useDataCache();
  const { data: apiData, isLoading, error, refetch } = useLeague(gameworldId, leagueId);

  // Sync API data to cache when it changes
  useEffect(() => {
    if (apiData) {
      setLeague(apiData);
    }
  }, [apiData, setLeague]);

  // Check if we should refetch based on cache staleness
  const shouldRefetch = isCacheStale(cache.lastUpdated.league, 360); // 6 hours for league (updates twice daily)

  return {
    data: cache.league || apiData,
    isLoading,
    error,
    refetch,
    shouldRefetch,
  };
};

/**
 * Hook that provides a unified interface for all cached data
 */
export const useCachedGameData = (gameworldId?: string, teamId?: string, leagueId?: string) => {
  const manager = useCachedManager();
  const team = useCachedTeam(gameworldId, teamId);
  const transferListPlayers = useCachedTransferListPlayers(gameworldId, {});
  const myBidsPlayers = useCachedMyBidsPlayers(gameworldId);
  const myActiveTransfers = useCachedMyActiveTransfers(gameworldId);
  const scoutedPlayers = useCachedScoutedPlayers(gameworldId, teamId);
  const fixtures = useCachedFixtures(gameworldId, leagueId, teamId);

  const isLoading = manager.isLoading || team.isLoading || transferListPlayers.isLoading;
  const hasError = manager.error || team.error || transferListPlayers.error;

  // Function to refresh all stale data
  const refreshStaleData = async () => {
    const promises = [];

    if (manager.shouldRefetch) {
      logger.log('Refreshing stale manager data');
      promises.push(manager.refetch());
    }

    if (team.shouldRefetch) {
      logger.log('Refreshing stale team data');
      promises.push(team.refetch());
    }

    if (transferListPlayers.shouldRefetch) {
      logger.log('Refreshing stale transfer list data');
      promises.push(transferListPlayers.refetch());
    }

    if (myBidsPlayers.shouldRefetch) {
      logger.log('Refreshing stale my bids data');
      promises.push(myBidsPlayers.refetch());
    }

    if (myActiveTransfers.shouldRefetch) {
      logger.log('Refreshing stale my active transfers data');
      promises.push(myActiveTransfers.refetch());
    }

    if (fixtures.shouldRefetch) {
      logger.log('Refreshing stale fixtures data');
      promises.push(fixtures.refetch());
    }

    if (scoutedPlayers.shouldRefetch) {
      logger.log('Refreshing stale scouted players data');
      promises.push(scoutedPlayers.refetch());
    }

    if (promises.length > 0) {
      await Promise.all(promises);
    }
  };

  return {
    manager: manager.manager,
    team: team.team,
    transferListPlayers: transferListPlayers.data,
    myBidsPlayers: myBidsPlayers.data,
    myActiveTransfers: myActiveTransfers.data,
    fixtures: fixtures.data,
    scoutedPlayers: scoutedPlayers.data,
    isLoading,
    error: hasError,
    refreshStaleData,
    updateManager: manager.updateManager,
    updateTeam: team.updateTeam,
  };
};

/**
 * Hook that provides cached player details data and automatically syncs with API
 */
export const useCachedPlayerDetails = (gameworldId?: string, playerId?: string) => {
  const { getPlayerDetails, setPlayerDetails } = useDataCache();
  const { data: apiData, isLoading, error, refetch } = usePlayerDetails(gameworldId, playerId);

  // Sync API data to cache when it changes
  useEffect(() => {
    if (apiData) {
      setPlayerDetails(apiData);
    }
  }, [apiData, setPlayerDetails]);

  // Get cached data
  const cachedData = playerId ? getPlayerDetails(playerId) : null;

  // Check if we should refetch based on cache staleness
  const shouldRefetch = useMemo(() => {
    if (!playerId || !cachedData) return false;
    // Player details are considered stale after 10 minutes
    // We need to get the actual timestamp from cache
    return false; // For now, disable staleness check to debug
  }, [playerId, cachedData]);

  return {
    data: cachedData || apiData,
    isLoading,
    error,
    refetch,
    shouldRefetch,
  };
};
